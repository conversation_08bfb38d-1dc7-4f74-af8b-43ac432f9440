import os
from pynamodb.models import Model
from pynamodb.attributes import UnicodeAtt<PERSON>bute, BooleanAttribute, NumberAttribute


class ServerStatus(Model):
    """
    A PynamoDB model for the SoftEther server status table.
    """

    class Meta:
        table_name = os.environ.get("DDB_TABLE_NAME", "ServerConnectivityStatus")
        region = os.environ.get("AWS_REGION", "ap-southeast-2")

    # Primary key attributes
    Customer = UnicodeAttribute(hash_key=True)
    ServerName = UnicodeAttribute(range_key=True)

    # Data attributes
    IsOnline = BooleanAttribute(default=False)
    AlertsEnabled = BooleanAttribute(default=True)
    UpdatedAt = NumberAttribute(null=True)
    TTL = NumberAttribute(null=True)  # TTL attribute for automatic record expiration
