import os
from typing import Any
from dataclasses import dataclass, fields

from netsuite.client import Config, NetSuite


@dataclass
class SupportCaseField:
    name: str
    ref_name: str | None = None
    id: str | None = None

    def __post_init__(self) -> None:
        if self.ref_name is None and self.id is None:
            raise ValueError("Either ref_name or id must be set")

    def to_dict(self) -> dict[str, Any]:
        if self.ref_name is not None:
            return {"refName": self.ref_name}

        return {"id": self.id}


@dataclass
class SupportCase:
    eid: str
    title: str | None = None
    emails: list[str] | None = None
    incoming_message: str | None = None
    site_id: str | None = None

    company: SupportCaseField | None = None
    status: SupportCaseField | None = None
    origin: SupportCaseField | None = None
    category: SupportCaseField | None = None
    priority: SupportCaseField | None = None
    product: SupportCaseField | None = None

    def to_payload(self) -> dict[str, Any]:
        body: dict[str, Any] = {}

        if self.title is not None:
            body["title"] = self.title

        if self.emails is not None:
            body["email"] = ", ".join(self.emails)

        if self.incoming_message is not None:
            # difference in production Netsuite form
            is_production = os.environ.get("ENVIRONMENT", "").lower() == "prod"
            body["custevent_parker_2020" if is_production else "incomingMessage"] = self.incoming_message

        if self.site_id is not None:
            body["custevent_f5_site_id"] = self.site_id

        for f in fields(self):
            value = getattr(self, f.name)
            if isinstance(value, SupportCaseField):
                body[value.name] = value.to_dict()

        return body


class Netsuite:
    def __init__(self) -> None:
        config = Config.from_env()
        self.ns = NetSuite(config)

    async def create_support_case(self, case: SupportCase) -> None:
        required = (
            "eid",
            "title",
            "company",
            "origin",
            "status",
            "category",
            "priority",
            "product",
            "site_id",
        )
        missing = [name for name in required if getattr(case, name) is None]
        if missing:
            raise ValueError(f"Missing mandatory fields: {', '.join(missing)}")

        await self.ns.rest_api.put(
            f"/record/v1/supportCase/eid:{case.eid}",
            json=case.to_payload(),
        )

    async def get_support_case(self, eid: str) -> dict[str, Any]:
        return await self.ns.rest_api.get(f"/record/v1/supportCase/eid:{eid}")  # type: ignore[no-any-return]

    async def update_support_case(self, eid: str, **fields: Any) -> None:
        payload = SupportCase(eid=eid, **fields).to_payload()
        await self.ns.rest_api.patch(
            f"/record/v1/supportCase/eid:{eid}",
            json=payload,
        )
