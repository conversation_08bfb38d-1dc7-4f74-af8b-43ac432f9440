import os
import asyncio
import requests

from aws_lambda_powertools import Logger
from aws_lambda_powertools.utilities.data_classes import event_source
from aws_lambda_powertools.utilities.data_classes import DynamoDBStreamEvent
from aws_lambda_powertools.utilities.typing import LambdaContext
from aws_lambda_powertools.utilities.data_classes.dynamo_db_stream_event import (
    DynamoDBRecordEventName,
)
from netsuite.exceptions import NetsuiteAPIRequestError

from src.util import get_ticket_url
from src.models import ServerStatus
import src.functions.notify.messages as messages
from src.netsuite import Netsuite, SupportCase, SupportCaseField

logger = Logger()
ns = Netsuite()

IGNORE_CUSTOMERS = os.environ.get("IGNORE_CUSTOMERS", "").split(",")
SLACK_WEBHOOK_URL = os.environ.get("SLACK_WEBHOOK_URL")

NETSUITE_CUSTOMER = SupportCaseField(name="company", id=os.environ.get("NETSUITE_CUSTOMER_ID"))
NETSUITE_STATUS_PENDING = SupportCaseField(name="status", ref_name="Pending")
NETSUITE_STATUS_CLOSED = SupportCaseField(name="status", ref_name="Closed")
NETSUITE_ORIGIN_PLATFORM = SupportCaseField(name="origin", ref_name="Platform")
NETSUITE_CATEGORY_L2_SERVER_OFFLINE = SupportCaseField(name="category", ref_name="Eyecue Server Offline")
NETSUITE_PRIORITY_P1 = SupportCaseField(name="priority", ref_name="P1")
NETSUITE_PRODUCT_EYECUE = SupportCaseField(name="custevent_f5sup_producttype", ref_name="EyeCue")


@logger.inject_lambda_context(log_event=True)
@event_source(data_class=DynamoDBStreamEvent)
def handler(event: DynamoDBStreamEvent, context: LambdaContext):
    """
    Lambda function to handle DynamoDB stream events and notify about server status changes.
    This function is triggered by DynamoDB stream events and processes each record to check
    for changes in server status. If a server goes offline, it raises an outage ticket.
    If a server comes back online, it closes the outage ticket.
    """
    return asyncio.run(process(event))


async def process(event: DynamoDBStreamEvent):
    for record in event.records:
        if record.event_name != DynamoDBRecordEventName.MODIFY:
            continue

        assert record.dynamodb is not None

        # Convert DynamoDB stream data to ServerStatus objects using our model
        old_server = ServerStatus.from_raw_data(record.dynamodb["OldImage"])
        new_server = ServerStatus.from_raw_data(record.dynamodb["NewImage"])

        if new_server.Customer in IGNORE_CUSTOMERS:
            logger.info(
                f"Ignoring server {new_server.ServerName} for customer {new_server.Customer} "
                f"as it is in the ignore list ({IGNORE_CUSTOMERS})"
            )
            continue

        # Skip if either conversion failed
        if not old_server or not new_server:
            logger.warning("Could not deserialize server state from DynamoDB stream")
            continue

        # Skip if alerts are disabled for this server
        if not new_server.AlertsEnabled:
            logger.info(
                f"Alerts disabled for {new_server.Customer}/{new_server.ServerName}, skipping notification"
            )
            continue

        # Check for transition from online to offline
        if old_server.IsOnline and not new_server.IsOnline:
            eid = create_eid(new_server.ServerName, new_server.UpdatedAt)
            await raise_outage_ticket(eid, new_server)

            try:
                ticket = await ns.get_support_case(eid)
            except NetsuiteAPIRequestError as e:
                if e.status_code == 404:
                    logger.warning(
                        f"Support case not found immediately after creation for "
                        f"{new_server.Customer}/{new_server.ServerName} with EID {eid}. "
                        f"This may indicate a NetSuite API issue. Skipping Slack notification.",
                        extra={
                            "server_name": new_server.ServerName,
                            "customer": new_server.Customer,
                            "eid": eid,
                            "status_code": e.status_code,
                        }
                    )
                    continue
                else:
                    # Re-raise for other NetSuite API errors
                    raise

            # When going offline, store the current time as the offline timestamp
            slack_notify(
                messages.slack_offline_message(
                    new_server.Customer,
                    new_server.ServerName,
                    get_ticket_url(ns.ns._config.account_slugified, ticket["id"]),
                )
            )

            logger.info(
                f"Outage ticket raised for {new_server.Customer}/{new_server.ServerName}"
            )

        # Check for transition from offline to online
        if not old_server.IsOnline and new_server.IsOnline:
            eid = create_eid(new_server.ServerName, old_server.UpdatedAt)

            try:
                ticket = await ns.get_support_case(eid)
            except NetsuiteAPIRequestError as e:
                if e.status_code == 404:
                    logger.warning(
                        f"Support case not found for {new_server.Customer}/{new_server.ServerName} "
                        f"with EID {eid}. This may indicate the original outage ticket was not created "
                        f"or was created with a different EID. Skipping Slack notification.",
                        extra={
                            "server_name": new_server.ServerName,
                            "customer": new_server.Customer,
                            "eid": eid,
                            "status_code": e.status_code,
                        }
                    )
                    continue
                else:
                    # Re-raise for other NetSuite API errors
                    raise

            # Calculate downtime using new_server.UpdatedAt (current time) and old_server.UpdatedAt
            downtime_minutes = int((new_server.UpdatedAt - old_server.UpdatedAt) / 60)
            slack_notify(
                messages.slack_online_message(
                    new_server.Customer,
                    new_server.ServerName,
                    downtime_minutes,
                    get_ticket_url(ns.ns._config.account_slugified, ticket["id"]),
                )
            )

            try:
                await close_outage_ticket(eid)
                logger.info(
                    f"Outage ticket closed for {new_server.Customer}/{new_server.ServerName}"
                )
            except NetsuiteAPIRequestError as e:
                if e.status_code == 404:
                    logger.warning(
                        f"Could not close support case for {new_server.Customer}/{new_server.ServerName} "
                        f"with EID {eid} - case not found. It may have been manually closed or deleted.",
                        extra={
                            "server_name": new_server.ServerName,
                            "customer": new_server.Customer,
                            "eid": eid,
                            "status_code": e.status_code,
                        }
                    )
                else:
                    # Re-raise for other NetSuite API errors
                    raise

    return {"statusCode": 200}


def create_eid(server_name: str, updated_at: float) -> str:
    """
    Create a unique EID for the support case based on server name and last updated timestamp.
    This EID is used to identify the support case in Netsuite. We can't use just the server name
    as it would continue to open/close the same ticket.
    """
    return f"{server_name}_{int(updated_at)}"


async def raise_outage_ticket(eid: str, server: ServerStatus):
    """
    Raise an outage ticket in Netsuite for the specified server.
    This function creates a support case in Netsuite with the provided details.
    """
    await ns.create_support_case(
        SupportCase(
            eid=eid,
            title=f"[SERVER OFFLINE] {server.ServerName}",
            site_id=server.ServerName,
            company=NETSUITE_CUSTOMER,
            status=NETSUITE_STATUS_PENDING,
            origin=NETSUITE_ORIGIN_PLATFORM,
            category=NETSUITE_CATEGORY_L2_SERVER_OFFLINE,
            priority=NETSUITE_PRIORITY_P1,
            product=NETSUITE_PRODUCT_EYECUE,
            incoming_message=messages.netsuite_message(),
        )
    )


async def close_outage_ticket(eid: str):
    """
    Close the outage ticket in Netsuite for the specified server.
    This function updates the support case in Netsuite to mark it as closed.
    """
    await ns.update_support_case(
        eid=eid,
        status=NETSUITE_STATUS_CLOSED,
    )


def slack_notify(message: str):
    """
    Send a notification to Slack.
    """
    if not SLACK_WEBHOOK_URL:
        logger.warning("SLACK_WEBHOOK_URL is not set, skipping Slack notification")
        return

    requests.post(
        SLACK_WEBHOOK_URL,
        headers={"Content-Type": "application/json"},
        json={"text": message},
    )
