def netsuite_message() -> str:
    return """Hey Team,

Our network monitoring has alerted us that your EyeCue Server responsible for all your drive-thru dashboards is down.
We hope everything is OK at your site and perhaps this is a scheduled power outage?

The next step in troubleshooting would be to Restart the EyeCue server.
It will most likely be in a cabinet somewhere (probably the store office) and looks like a big black box with ThinkStation in silver on the front of it.
Its about the size of a large PC.

Hold down the power button for a few seconds until it powers off, wait for 20 seconds for a complete power down and then power it back on again. Your dashboards should all come back in the next 5 minutes or so, but if not, their associated Chromeboxes may also need to be rebooted (again).
Let us know how you get on by replying to this message.
Thanks,

The EYECUE Team
"""


def slack_online_message(
    customer: str, server_name: str, downtime_minutes: int, netsuite_url: str
) -> str:
    return (
        f":green-alert: [{customer}] *{server_name}* is back online after "
        f"{downtime_minutes} minutes. View the ticket here: <{netsuite_url}|Netsuite Ticket>"
    )


def slack_offline_message(customer: str, server_name: str, netsuite_url: str) -> str:
    return (
        f":orange_alert: [{customer}] *{server_name}* has gone offline. "
        f"View the ticket here: <{netsuite_url}|Netsuite Ticket>"
    )
