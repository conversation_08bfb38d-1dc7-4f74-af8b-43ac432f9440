import os
from collections.abc import Callable

import prometheus_client as prom
from aws_lambda_powertools import Logger
from aws_lambda_powertools.utilities.typing import LambdaContext
from prometheus_client.exposition import basic_auth_handler

from src.models import ServerStatus

logger = Logger()

VICTORIA_METRICS_URL = os.environ["VICTORIA_METRICS_URL"]
VICTORIA_METRICS_USERNAME = os.environ["VICTORIA_METRICS_USERNAME"]
VICTORIA_METRICS_PASSWORD = os.environ["VICTORIA_METRICS_PASSWORD"]
ENVIRONMENT = os.environ.get("ENVIRONMENT", "dev")


@logger.inject_lambda_context
def handler(_event: dict, _context: LambdaContext) -> dict:
    """Lambda function to update Victoria Metrics gauges with server status.

    This function runs on a schedule and reads all server statuses from DynamoDB.
    """
    registry = prom.CollectorRegistry()
    server_alive = prom.Gauge(
        "alive_status",
        "Indicates if the server is alive (1) or not (0)",
        labelnames=["site_id", "environment"],
        registry=registry,
        namespace="eyecue_server",
    )

    for server in ServerStatus.scan():
        metric_value = 1 if server.IsOnline else 0
        server_alive.labels(server.ServerName, ENVIRONMENT).set(metric_value)

    prom.push_to_gateway(
        VICTORIA_METRICS_URL,
        job="server-connectivity-alerts",
        registry=registry,
        handler=_auth_handler,
    )

    return {"statusCode": 200}


def _auth_handler(
    url: str,
    method: str,
    timeout: float | None,
    headers: list[tuple[str, str]],
    data: bytes,
) -> Callable[[], None]:
    """Create an authentication handler for Victoria Metrics push gateway.

    https://prometheus.github.io/client_python/exporting/pushgateway/#handlers-for-authentication
    """
    return basic_auth_handler(
        url,
        method,
        timeout,
        headers,
        data,
        VICTORIA_METRICS_USERNAME,
        VICTORIA_METRICS_PASSWORD,
    )
