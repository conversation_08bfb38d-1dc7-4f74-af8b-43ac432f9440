import unittest
from unittest.mock import patch, MagicMock
from src.softether import <PERSON>E<PERSON>, Hu<PERSON>, Session, User


class TestSoftEther(unittest.TestCase):

    def setUp(self):
        # Set up SoftEther client
        self.client = SoftEther(
            host="mock.vpn.host", port=443, hubname="TEST-HUB", password="mockpassword"
        )

    @patch("src.softether.requests.session")
    def test_client_initialization(self, mock_session):
        # Test that the client is initialized properly
        mock_session_instance = MagicMock()
        mock_session.return_value = mock_session_instance

        client = SoftEther(
            host="mock.vpn.host",
            port=443,
            hubname="TEST-HUB",
            password="mockpassword",
            verify=False,
        )

        # Assert session was created
        mock_session.assert_called_once()

        # Assert verify is set to False
        self.assertEqual(mock_session_instance.verify, False)

        # Assert headers are set correctly
        mock_session_instance.headers.update.assert_called_once_with(
            {
                "Content-Type": "application/json",
                "X-VPNADMIN-HUBNAME": "TEST-HUB",
                "X-VPNADMIN-PASSWORD": "mockpassword",
            }
        )

    @patch("requests.Session.post")
    def test_send_request(self, mock_post):
        # Setup mock response
        mock_response = MagicMock()
        mock_response.json.return_value = {"result": {"success": True}}
        mock_response.raise_for_status = MagicMock()
        mock_post.return_value = mock_response

        # Call the method
        result = self.client._send_request("TestMethod", {"param1": "value1"})

        # Verify the correct URL and data were used
        mock_post.assert_called_once()
        call_args = mock_post.call_args[1]
        self.assertIn("json", call_args)

        request_data = call_args["json"]
        self.assertEqual(request_data["jsonrpc"], "2.0")
        self.assertEqual(request_data["method"], "TestMethod")
        self.assertEqual(request_data["params"], {"param1": "value1"})

        # Verify response was processed
        mock_response.raise_for_status.assert_called_once()
        mock_response.json.assert_called_once()
        self.assertEqual(result, {"result": {"success": True}})

    @patch("src.softether.requests.session")
    def test_enum_hub(self, mock_session):
        # Setup mock client
        mock_client = MagicMock()
        mock_session.return_value = mock_client

        # Setup mock response
        mock_response = {
            "result": {
                "HubList": [
                    {
                        "HubName_str": "Hub1",
                        "HubType_u32": 0,
                        "Online_bool": True,
                        "NumSessions_u32": 5,
                        "LastCommTime_dt": "2023-01-01T00:00:00.000Z",
                        "CreatedTime_dt": "2022-01-01T00:00:00.000Z",
                        "LastLoginTime_dt": "2023-01-01T00:00:00.000Z",
                        "IsTrafficFilled_bool": False,
                        "NumGroups_u32": 3,
                        "NumUsers_u32": 10,
                        "NumMacTables_u32": 0,
                        "NumIpTables_u32": 0,
                        "NumLogin_u32": 100,
                    }
                ]
            }
        }

        # Mock the _send_request method
        client = SoftEther(
            host="mock.vpn.host", port=443, hubname="TEST-HUB", password="mockpassword"
        )
        client._send_request = MagicMock(return_value=mock_response)

        # Call the method
        result = client.enum_hub()

        # Verify the correct method was called
        client._send_request.assert_called_once_with("EnumHub")

        # Verify the result was parsed correctly
        self.assertEqual(len(result), 1)
        self.assertIn("HubName_str", result[0])
        self.assertEqual(result[0]["HubName_str"], "Hub1")
        self.assertEqual(result[0]["NumSessions_u32"], 5)
        self.assertTrue(result[0]["Online_bool"])

    @patch("src.softether.requests.session")
    def test_enum_session(self, mock_session):
        # Setup mock client
        mock_client = MagicMock()
        mock_session.return_value = mock_client

        # Setup mock response
        mock_response = {
            "result": {
                "SessionList": [
                    {
                        "Name_str": "Session1",
                        "Username_str": "User1",
                        "Hostname_str": "host1.example.com",
                        "RemoteHostname_str": "remote.example.com",
                        "ClientIP_ip": "*************",
                        "UniqueId_bin": "abc123",
                        "MaxNumTcp_u32": 100,
                        "CurrentNumTcp_u32": 5,
                        "PacketSize_u64": 1024,
                        "PacketNum_u64": 42,
                        "RemoteSession_bool": False,
                        "LinkMode_bool": False,
                        "SecureNATMode_bool": False,
                        "BridgeMode_bool": False,
                        "Layer3Mode_bool": False,
                        "Client_BridgeMode_bool": False,
                        "Client_MonitorMode_bool": False,
                        "VLanId_u32": 0,
                        "IsDormant_bool": False,
                        "IsDormantEnabled_bool": False,
                        "LastCommTime_dt": "2023-01-01T00:00:00.000Z",
                        "LastCommDormant_dt": "2023-01-01T00:00:00.000Z",
                        "CreatedTime_dt": "2022-01-01T00:00:00.000Z",
                        "Ip_ip": "********",
                    }
                ]
            }
        }

        # Mock the _send_request method
        client = SoftEther(
            host="mock.vpn.host", port=443, hubname="TEST-HUB", password="mockpassword"
        )
        client._send_request = MagicMock(return_value=mock_response)

        # Call the method
        result = client.enum_session("TEST-HUB")

        # Verify the correct method was called with correct params
        client._send_request.assert_called_once_with(
            "EnumSession", params={"HubName_str": "TEST-HUB"}
        )

        # Verify the result was parsed correctly
        self.assertEqual(len(result), 1)
        self.assertIn("Name_str", result[0])
        self.assertEqual(result[0]["Name_str"], "Session1")
        self.assertEqual(result[0]["Username_str"], "User1")
        self.assertEqual(result[0]["ClientIP_ip"], "*************")

    @patch("src.softether.requests.session")
    def test_enum_user(self, mock_session):
        # Setup mock client
        mock_client = MagicMock()
        mock_session.return_value = mock_client

        # Setup mock response
        mock_response = {
            "result": {
                "UserList": [
                    {
                        "Name_str": "User1",
                        "GroupName_str": "Group1",
                        "Realname_utf": "John Doe",
                        "Note_utf": "Test user",
                        "AuthType_u32": 1,
                        "NumLogin_u32": 42,
                        "LastLoginTime_dt": "2023-01-01T00:00:00.000Z",
                        "DenyAccess_bool": False,
                        "IsTrafficFilled_bool": False,
                        "IsExpiresFilled_bool": True,
                        "Expires_dt": "2030-01-01T00:00:00.000Z",
                    }
                ]
            }
        }

        # Mock the _send_request method
        client = SoftEther(
            host="mock.vpn.host", port=443, hubname="TEST-HUB", password="mockpassword"
        )
        client._send_request = MagicMock(return_value=mock_response)

        # Call the method
        result = client.enum_user("TEST-HUB")

        # Verify the correct method was called with correct params
        client._send_request.assert_called_once_with(
            "EnumUser", params={"HubName_str": "TEST-HUB"}
        )

        # Verify the result was parsed correctly
        self.assertEqual(len(result), 1)
        self.assertIn("Name_str", result[0])
        self.assertEqual(result[0]["Name_str"], "User1")
        self.assertEqual(result[0]["GroupName_str"], "Group1")
        self.assertEqual(result[0]["Realname_utf"], "John Doe")
        self.assertFalse(result[0]["DenyAccess_bool"])


if __name__ == "__main__":
    unittest.main()
