# Server Down Alerts

This project provides an end‑to‑end solution for detecting and notifying on server connectivity issues. Using the Softether VPN's API we can detect outages and report these via Slack & Netsuite.

## Design

### Flow

1. A scheduled Lambda function polls the SoftEther VPN API
2. Server status changes are written to DynamoDB
3. DynamoDB Streams trigger notifications for state changes

### Monitoring

Each server's state is maintained within a DynamoDB table

| Customer | Server Name    | Alerts Enabled | Is Online | Updated At |
|----------|----------------|----------------|-----------|------------|
| TCV      | fm-tcv-nzl-009 | true           | true      | 1746567638 |

### Alerting

The system sends notifications through Slack & Netsuite when a server transitions between online and offline states. Alert logic includes:

- Notifications only for state changes (not every check)
- Ability to disable alerts for specific servers


## Setup

### Secret Management

You must create secrets in AWS Secrets Manager for storing sensitive configuration values. The following secrets with specific formats are required:

#### VPN Secret
Expected JSON structure:
```json
{
  "host": "vpn.example.com",
  "port": "5555",
  "password": "your_admin_password"
}
```

#### Slack Webhook Secret
Expected format: A plain string URL for the Slack webhook
```
*****************************************************************************
```

#### NetSuite Secret
Expected JSON structure:
```json
{
  "customer_id": "your_customer_id",
  "account": "your_netsuite_account",
  "consumer_key": "your_consumer_key",
  "consumer_secret": "your_consumer_secret",
  "token_id": "your_token_id",
  "token_secret": "your_token_secret"
}
```

#### Victoria Metrics Secret
Expected JSON structure:
```json
{
  "VICTORIA_METRICS_URL": "https://localhost:8427/api/v1/import/prometheus",
  "VICTORIA_METRICS_USERNAME": "your_username",
  "VICTORIA_METRICS_PASSWORD": "your_password"
}
```

Each of these secrets is referenced in the SAM template and passed to the appropriate Lambda functions as environment variables.
You can substitute these as SAM arguments.

## Netsuite

### Get Netsuite Token

Setup ▸ Integration ▸ Manage Integrations ▸ New

![Integration Page](./docs/netsuite_setup_intergration.png)

Copy the Consumer Key and Consumer Secret Under Client Credentials

> Warning: On refresh these fields will be hidden permanently so copy them down

Main Dashboard scroll down and find Settings ▸ Manage Access Tokens ▸ New My Access Token

> Warning: You cannot create these via Setup ▸ Users/Roles ▸ Access Tokens because netsuite's engineer thought it would be funny...

A platform team tool to report on server outages via Netsuite's support case API.

![Access Tokens](./docs/netsuite_setup_intergration.png)

> Warning: On refresh these fields will be hidden permanently so copy them down

### Creating Required Fields

You may need to verify that the applications required fields are present in the Netsuite Settings.
If the following are NOT present, you will need to create these by clicking NEW under the following pages.
These pages can be found under **Setup ▸ Support**

> Note: These are case sensitive!
