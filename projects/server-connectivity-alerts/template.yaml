AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: SAM template that sets up a SoftEther monitoring system using DynamoDB streams for offline alerts.

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues:
      - dev
      - prod
    Description: Deployment environment

  VpnSecretArn:
    Type: String
    Description: ARN of the Secrets Manager secret that stores VPN connection info
    Default: arn:aws:secretsmanager:ap-southeast-2:979525481730:secret:server-connectivity-monitor/softether-vpn-a2oonK

  SlackWebHookSecretArn:
    Type: String
    Description: ARN of the Secrets Manager secret that stores the Slack webhook URL
    Default: arn:aws:secretsmanager:ap-southeast-2:979525481730:secret:server-connectivity-monitor/slack-webhook-url-fLg6f0

  NetSuiteSecretArn:
    Type: String
    Description: ARN of the Secrets Manager secret that stores NetSuite credentials
    Default: arn:aws:secretsmanager:ap-southeast-2:979525481730:secret:server-connectivity-monitor/netsuite-QA3Vru

  MetricsSecretArn:
    Type: String
    Description: ARN of the Secrets Manager secret that stores VictoriaMetrics credentials
    Default: arn:aws:secretsmanager:ap-southeast-2:979525481730:secret:server-connectivity-monitor/victoria-metrics-gvZ0Gy

  AlarmSNSArn:
    Type: String
    Description: ARN of the SNS topic to notify when the functions fail.
    Default: ""

  IgnoreHubs:
    Type: String
    Description: Comma-separated list of hub names to ignore during monitoring
    Default: "FM-TEAM"

  IgnoreCustomers:
    Type: String
    Description: Comma-separated list of customer names to ignore during notifications
    Default: "TST,DEV,STG,POC"

Globals:
  Function:
    Runtime: python3.13
    Timeout: 300  # Increased from 60s to handle large hubs like mcd-aus
    MemorySize: 256

Conditions:
  AlarmSNSEnabled: !Not [!Equals [!Ref AlarmSNSArn, ""]]

Resources:
  ####################################################
  # 1) DynamoDB Table
  ####################################################
  ServerConnectivityStatusTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: server-connectivity-alert-status
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
      - AttributeName: Customer
        AttributeType: S
      - AttributeName: ServerName
        AttributeType: S
      KeySchema:
      - AttributeName: Customer
        KeyType: HASH
      - AttributeName: ServerName
        KeyType: RANGE
      TimeToLiveSpecification:
        AttributeName: TTL
        Enabled: true
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      DeletionProtectionEnabled: true
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true

  ####################################################
  # 2) Monitor Lambda
  #    - Periodically queries SoftEther
  #    - Updates DynamoDB with latest server statuses
  ####################################################
  MonitorFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src.functions.monitor.app.handler
      Description: Queries SoftEther and updates DDB with server statuses
      Policies:
      - AWSLambdaBasicExecutionRole
      - AmazonDynamoDBFullAccess
      - Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Action: secretsmanager:GetSecretValue
            Resource:
              - !Ref VpnSecretArn
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          VPN_HOST: !Sub "{{resolve:secretsmanager:${VpnSecretArn}:SecretString:host}}"
          VPN_PORT: !Sub "{{resolve:secretsmanager:${VpnSecretArn}:SecretString:port}}"
          VPN_PASS: !Sub "{{resolve:secretsmanager:${VpnSecretArn}:SecretString:password}}"
          OFFLINE_THRESHOLD_MINUTES: 5
          IGNORE_HUBS: !Ref IgnoreHubs
          DDB_TABLE_NAME:
            Ref: ServerConnectivityStatusTable
      Events:
        MonitorSchedule:
          Type: Schedule
          Properties:
            Schedule: rate(1 minute)

  ####################################################
  # 3) Notifier Lambda
  #    - Triggered by DynamoDB Stream
  #    - Sends Slack alerts for offline servers
  ####################################################
  NotifierFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src.functions.notify.app.handler
      Description: Triggered by changes in the DDB stream, sends Slack notifications
      ReservedConcurrentExecutions: 1  # Only one execution at a time to avoid race conditions
      Policies:
      - AWSLambdaBasicExecutionRole
      - AmazonDynamoDBFullAccess
      - Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Action: secretsmanager:GetSecretValue
            Resource:
              - !Ref SlackWebHookSecretArn
              - !Ref NetSuiteSecretArn
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          IGNORE_CUSTOMERS: !Ref IgnoreCustomers
          SLACK_WEBHOOK_URL: !Sub "{{resolve:secretsmanager:${SlackWebHookSecretArn}:SecretString}}"
          NETSUITE_CUSTOMER_ID: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:customer_id}}"
          NETSUITE_ACCOUNT: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:account}}"
          NETSUITE_CONSUMER_KEY: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:consumer_key}}"
          NETSUITE_CONSUMER_SECRET: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:consumer_secret}}"
          NETSUITE_TOKEN_ID: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:token_id}}"
          NETSUITE_TOKEN_SECRET: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:token_secret}}"
      Events:
        TableStreamEvent:
          Type: DynamoDB
          Properties:
            Stream:
              Fn::GetAtt:
              - ServerConnectivityStatusTable
              - StreamArn
            StartingPosition: LATEST

  ####################################################
  # 4) Monitor Lambda
  #    - Periodically queries DynamoDB for server statuses
  #    - Pushes metrics to VictoriaMetrics
  ####################################################
  MetricsFunction:
    Type: AWS::Serverless::Function
    Properties:
      Handler: src.functions.metrics.app.handler
      Description: Pushes server connectivity metrics to VictoriaMetrics
      Timeout: 300
      Policies:
        - AWSLambdaBasicExecutionRole
        - AmazonDynamoDBReadOnlyAccess
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          VICTORIA_METRICS_URL: !Sub "{{resolve:secretsmanager:${MetricsSecretArn}:SecretString:VICTORIA_METRICS_URL}}"
          VICTORIA_METRICS_USERNAME: !Sub "{{resolve:secretsmanager:${MetricsSecretArn}:SecretString:VICTORIA_METRICS_USERNAME}}"
          VICTORIA_METRICS_PASSWORD: !Sub "{{resolve:secretsmanager:${MetricsSecretArn}:SecretString:VICTORIA_METRICS_PASSWORD}}"
          DDB_TABLE_NAME:
            Ref: ServerConnectivityStatusTable
      Events:
        MetricsSchedule:
          Type: Schedule
          Properties:
            Schedule: rate(5 minutes)

  ######################################################
  # Cloudwatch Alarms
  #######################################################

  # ---- Monitor Function Alarms ----

  MonitorFunctionFailureAlarm:
    Type: AWS::CloudWatch::Alarm
    Condition: AlarmSNSEnabled
    Properties:
      AlarmDescription: "Alarm when the MonitorFunction fails"
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 60
      EvaluationPeriods: 1
      Threshold: 0
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: FunctionName
          Value: !Ref MonitorFunction
      AlarmActions:
        - !Ref AlarmSNSArn
      OKActions:
        - !Ref AlarmSNSArn


  # ---- Notifier Function Alarms ----

  NotifierFunctionFailureAlarm:
    Type: AWS::CloudWatch::Alarm
    Condition: AlarmSNSEnabled
    Properties:
      AlarmDescription: "Alarm when the NotifierFunction fails"
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 60
      EvaluationPeriods: 1
      Threshold: 0
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: FunctionName
          Value: !Ref NotifierFunction
      AlarmActions:
        - !Ref AlarmSNSArn
      OKActions:
        - !Ref AlarmSNSArn

Outputs:
  ServerConnectivityStatusDDBTable:
    Description: Name of the DynamoDB table storing SoftEther server statuses.
    Value:
      Ref: ServerConnectivityStatusTable
