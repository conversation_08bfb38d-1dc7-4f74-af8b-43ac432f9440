AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  Netsuite SNS Alerter - A generic alerter for Netsuite Support Tickets via SNS events.

Globals:
  Function:
    Runtime: python3.13
    Timeout: 300
    MemorySize: 128
    Environment:
      Variables:
        POWERTOOLS_SERVICE_NAME: !Ref AWS::StackName
        POWERTOOLS_LOG_LEVEL: !Ref LogLevel
        POWERTOOLS_LOGGER_LOG_EVENT: !Ref LogEvent
    Tags:
      Environment: !Ref Environment
      Squad: platform

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues:
      - dev
      - prod
    Description: Deployment environment

  LogLevel:
    Type: String
    Default: INFO
    AllowedValues:
      - DEBUG
      - INFO
      - WARNING
      - ERROR
      - CRITICAL
    Description: Log level for the Lambda function

  LogEvent:
    Type: String
    Default: "false"
    AllowedValues:
      - "true"
      - "false"
    Description: Log event for the Lambda function

  NetSuiteSecretArn:
    Type: String
    Description: ARN of the Secrets Manager secret that stores NetSuite credentials
    Default: arn:aws:secretsmanager:ap-southeast-2:979525481730:secret:camera-connectivity-alerts/netsuite-0u4RLy

  AlarmSNSArn:
    Type: String
    Description: ARN of the SNS topic to notify when the handler fails
    Default: ""

Conditions:
  AlarmSNSEnabled: !Not [!Equals [!Ref AlarmSNSArn, ""]]

Resources:
  # SNS Topic for receiving alerts
  AlertsTopic:
    Type: AWS::SNS::Topic
    Properties:
      DisplayName: !Sub "Netsuite SNS Alerts (${Environment})"
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Squad
          Value: platform

  # Single Lambda function to process alerts and send notifications
  ProcessAlertsFunction:
    Type: AWS::Serverless::Function
    Properties:
      FunctionName: !Sub "netsuite-sns-alerts-${Environment}"
      Handler: src.functions.app.handler
      CodeUri: .
      Description: "Processes alerts for Grafana and creates NetSuite tickets"
      Environment:
        Variables:
          ENVIRONMENT: !Ref Environment
          NETSUITE_CUSTOMER_ID: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:customer_id}}"
          NETSUITE_ACCOUNT: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:account}}"
          NETSUITE_CONSUMER_KEY: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:consumer_key}}"
          NETSUITE_CONSUMER_SECRET: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:consumer_secret}}"
          NETSUITE_TOKEN_ID: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:token_id}}"
          NETSUITE_TOKEN_SECRET: !Sub "{{resolve:secretsmanager:${NetSuiteSecretArn}:SecretString:token_secret}}"
      Events:
        SNSEvent:
          Type: SNS
          Properties:
            Topic: !Ref AlertsTopic

  # ---- Process Alerts Function Alarms ----

  ProcessAlertsFunctionAlarm:
    Type: AWS::CloudWatch::Alarm
    Condition: AlarmSNSEnabled
    Properties:
      AlarmDescription: "Alarm when the ProcessAlertsFunction fails"
      MetricName: Errors
      Namespace: AWS/Lambda
      Statistic: Sum
      Period: 60
      EvaluationPeriods: 1
      Threshold: 0
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: FunctionName
          Value: !Ref ProcessAlertsFunction
      AlarmActions:
        - !Ref AlarmSNSArn
      OKActions:
        - !Ref AlarmSNSArn

  # IAM User for SNS Publishing
  SNSPublisherUser:
    Type: AWS::IAM::User
    Properties:
      UserName: !Sub "netsuite-sns-publisher-${Environment}"
      Tags:
        - Key: Environment
          Value: !Ref Environment
        - Key: Squad
          Value: platform

  # IAM Policy for SNS Publish permissions
  SNSPublishPolicy:
    Type: AWS::IAM::Policy
    Properties:
      PolicyName: !Sub "netsuite-sns-publish-policy-${Environment}"
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - sns:Publish
            Resource: !Ref AlertsTopic
      Users:
        - !Ref SNSPublisherUser

  # Access Keys for the SNS Publisher User
  SNSPublisherAccessKey:
    Type: AWS::IAM::AccessKey
    Properties:
      UserName: !Ref SNSPublisherUser

Outputs:
  SNSTopicArn:
    Description: "SNS Topic ARN for alerts"
    Value: !Ref AlertsTopic

  SNSPublisherUserName:
    Description: "IAM User name for SNS publishing"
    Value: !Ref SNSPublisherUser

  SNSPublisherAccessKeyId:
    Description: "Access Key ID for SNS Publisher User"
    Value: !Ref SNSPublisherAccessKey

  SNSPublisherSecretAccessKey:
    Description: "Secret Access Key for SNS Publisher User"
    Value: !GetAtt SNSPublisherAccessKey.SecretAccessKey

