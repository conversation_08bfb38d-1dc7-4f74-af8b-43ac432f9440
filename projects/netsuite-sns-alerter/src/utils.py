import hashlib
import json
from src.models import <PERSON><PERSON>


def generate_stable_eid(alert: Alert) -> str:
    """
    Generates a stable, deterministic SHA-256 hash from a dictionary of labels.
    The labels are sorted by key to ensure a canonical representation.
    """
    # Create a canonical representation by sorting keys
    canonical_labels = json.dumps(alert.labels, sort_keys=True)

    # Create a SHA-256 hash
    sha256_hash = hashlib.sha256(canonical_labels.encode('utf-8')).hexdigest()

    return sha256_hash
