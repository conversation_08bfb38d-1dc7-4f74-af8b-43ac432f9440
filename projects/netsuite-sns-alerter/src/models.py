from typing import Literal
from pydantic import BaseModel, Field


class Alert(BaseModel):
    status: Literal["firing", "resolved"] = Field(description="Status of the alert")
    title: str = Field(description="Title of the alert")
    message: str | None = Field(description="Description of the alert", default=None)
    site_id: str = Field(description="Site ID of the alert")
    labels: dict[str, str]


class SNSMessage(BaseModel):
    """A Pydantic model for the SNS message."""

    alerts: list[Alert] = Field(
        default_factory=list,
        description="List of alerts",
    )
