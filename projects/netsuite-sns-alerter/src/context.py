import os
from dataclasses import dataclass, field
from netsuite.client import Config

from .ns import SupportCaseField, Netsuite
from .services.alert_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .services.downtime_service import DowntimeService


@dataclass
class Fields:
    NETSUITE_COMPANY: SupportCaseField = field(
        default_factory=lambda: SupportCaseField(
            name="company", id=os.environ["NETSUITE_CUSTOMER_ID"]
        )
    )
    NETSUITE_STATUS_PENDING: SupportCaseField = field(
        default_factory=lambda: SupportCaseField(
            name="status",
            ref_name=os.environ.get("NETSUITE_STATUS_PENDING", "Pending"),
        )
    )
    NETSUITE_STATUS_CLOSED: SupportCaseField = field(
        default_factory=lambda: SupportCaseField(
            name="status",
            ref_name=os.environ.get("NETSUITE_STATUS_CLOSED", "Closed"),
        )
    )
    NETSUITE_ORIGIN_PLATFORM: SupportCaseField = field(
        default_factory=lambda: SupportC<PERSON><PERSON>ield(
            name="origin",
            ref_name=os.environ.get("NETSUITE_ORIGIN_PLATFORM", "Platform"),
        )
    )
    NETSUITE_CATEGORY_L2_CAMERA_OFFLINE: SupportCaseField = field(
        default_factory=lambda: SupportCaseField(
            name="category",
            ref_name=os.environ.get(
                "NETSUITE_CATEGORY_L2_CAMERA_OFFLINE", "Eyecue Camera Offline"
            ),
        )
    )
    NETSUITE_PRIORITY_P3: SupportCaseField = field(
        default_factory=lambda: SupportCaseField(
            name="priority",
            ref_name=os.environ.get("NETSUITE_PRIORITY_P3", "P3"),
        )
    )
    NETSUITE_PRODUCT_EYECUE: SupportCaseField = field(
        default_factory=lambda: SupportCaseField(
            name="custevent_f5sup_producttype",
            ref_name=os.environ.get("NETSUITE_PRODUCT_EYECUE", "EyeCue"),
        )
    )


@dataclass
class Context:
    netsuite_fields: Fields = field(default_factory=Fields)
    netsuite_config: Config = field(default_factory=Config.from_env)
    netsuite: Netsuite = field(init=False)
    alert_handler: AlertHandler = field(init=False)
    downtime_service: DowntimeService = field(init=False)

    def __post_init__(self) -> None:
        self.netsuite = Netsuite(self.netsuite_config)
        self.downtime_service = DowntimeService()
        self.alert_handler = AlertHandler(self)
