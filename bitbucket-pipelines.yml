pipelines:
  custom:
    # ===========================
    # Camera Connectivity Alerts
    # ===========================

    deploy-camera-connectivity-alerts-dev:
      - step:
          name: "[INFRA-DEV] Camera Connectivity Alerts Deploy (ap-southeast-2)"
          deployment: infra-dev
          image: public.ecr.aws/sam/build-python3.13
          oidc: true
          script:
            - cd projects/camera-connectivity-alerts
            - sam build --use-container --debug
            - pipe: atlassian/aws-sam-deploy:2.4.1
              variables:
                AWS_DEFAULT_REGION: ap-southeast-2
                AWS_OIDC_ROLE_ARN: $AWS_DEPLOY_ROLE_ARN
                EXTRA_OPTIONS_DEPLOY: [
                  "--parameter-overrides",
                  "Environment=dev",
                  "LogLevel=DEBUG",
                  "LogEvent=true",
                  "SlackWebHookSecretArn=${CAMERA_CONNECTIVITY_SLACK_WEBHOOK_SECRET_ARN}",
                  "NetSuiteSecretArn=${CAMERA_CONNECTIVITY_NETSUITE_SECRET_ARN}",
                ]

    deploy-camera-connectivity-alerts-prod:
      - step:
          name: "[INFRA-PROD] Camera Connectivity Alerts Deploy (ap-southeast-2)"
          deployment: infra-prod
          image: public.ecr.aws/sam/build-python3.13
          oidc: true
          script:
            - cd projects/camera-connectivity-alerts
            - sam build --use-container --debug
            - pipe: atlassian/aws-sam-deploy:2.4.1
              variables:
                AWS_DEFAULT_REGION: ap-southeast-2
                AWS_OIDC_ROLE_ARN: $AWS_DEPLOY_ROLE_ARN
                EXTRA_OPTIONS_DEPLOY: [
                  "--parameter-overrides",
                  "Environment=prod",
                  "SlackWebHookSecretArn=${CAMERA_CONNECTIVITY_SLACK_WEBHOOK_SECRET_ARN}",
                  "NetSuiteSecretArn=${CAMERA_CONNECTIVITY_NETSUITE_SECRET_ARN}",
                  "AlarmSNSArn=arn:aws:sns:ap-southeast-2:055313672806:notify-slack",
                ]

    # ===========================
    # Server Connectivity Alerts
    # ===========================

    deploy-server-connectivity-alerts-dev:
      - step:
          name: "[INFRA-DEV] Server Connectivity Alerts Deploy (ap-southeast-2)"
          deployment: infra-dev
          image: public.ecr.aws/sam/build-python3.13
          oidc: true
          script:
            - cd projects/server-connectivity-alerts
            - sam --version
            - sam build --use-container --debug
            - pipe: atlassian/aws-sam-deploy:2.4.1
              variables:
                AWS_DEFAULT_REGION: ap-southeast-2
                AWS_OIDC_ROLE_ARN: $AWS_DEPLOY_ROLE_ARN
                EXTRA_OPTIONS_DEPLOY: [
                  "--parameter-overrides",
                  "Environment=dev",
                  "VpnSecretArn=${SERVER_CONNECTIVITY_SOFTETHER_SECRET_ARN}",
                  "SlackWebHookSecretArn=${SERVER_CONNECTIVITY_SLACK_WEBHOOK_SECRET_ARN}",
                  "NetSuiteSecretArn=${SERVER_CONNECTIVITY_NETSUITE_SECRET_ARN}",
                  "MetricsSecretArn=${SERVER_CONNECTIVITY_VICTORIA_METRICS_SECRET_ARN}",
                  "IgnoreHubs=${SERVER_CONNECTIVITY_IGNORE_HUBS}",
                  "IgnoreCustomers=${SERVER_CONNECTIVITY_IGNORE_CUSTOMERS}",
                ]

    deploy-server-connectivity-alerts-prod:
      - step:
          name: "[INFRA-PROD] Server Connectivity Alerts Deploy (ap-southeast-2)"
          deployment: infra-prod
          image: public.ecr.aws/sam/build-python3.13
          oidc: true
          script:
            - cd projects/server-connectivity-alerts
            - sam --version
            - sam build --use-container --debug
            - pipe: atlassian/aws-sam-deploy:2.4.1
              variables:
                AWS_DEFAULT_REGION: ap-southeast-2
                AWS_OIDC_ROLE_ARN: $AWS_DEPLOY_ROLE_ARN
                EXTRA_OPTIONS_DEPLOY: [
                  "--parameter-overrides",
                  "Environment=prod",
                  "VpnSecretArn=${SERVER_CONNECTIVITY_SOFTETHER_SECRET_ARN}",
                  "SlackWebHookSecretArn=${SERVER_CONNECTIVITY_SLACK_WEBHOOK_SECRET_ARN}",
                  "NetSuiteSecretArn=${SERVER_CONNECTIVITY_NETSUITE_SECRET_ARN}",
                  "MetricsSecretArn=${SERVER_CONNECTIVITY_VICTORIA_METRICS_SECRET_ARN}",
                  "IgnoreHubs=${SERVER_CONNECTIVITY_IGNORE_HUBS}",
                  "IgnoreCustomers=${SERVER_CONNECTIVITY_IGNORE_CUSTOMERS}",
                  "AlarmSNSArn=arn:aws:sns:ap-southeast-2:055313672806:notify-slack",
                ]
